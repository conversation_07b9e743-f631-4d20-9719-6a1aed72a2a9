# TODO


## WhatsApp: 
- [x] add error handling for message sending
- [x] add hook for message receving
- [ ] alternative to payemoji

Session management: 
- [x] integrate Redis




## Features
-  [ ] Multiple restaurant support in restaurant app? 
   -  [ ] maximum 5?: use more than 1 app? or same app to handle different restaurants?
- [x] Google pay
- [x] Adjust UI for order link
- [x] Fix address bug for the address management
- [ ] Upload menu

## Test
- [x] Test framework: unit
- [x] Test framework: integration
- [ ] Test scripts: unit - ongoing
- [ ] Test scripts: integration - ongoing
- [ ] Test coverage: unit - ongoing
- [ ] Test coverage: integration - ongoing
- [ ] stress test



## Setup server
- [ ] how to handle log
- [ ] how to handle error
- [ ] 


## Security
- [ ] Verify the authentication & right of the webhook request and whatsapp request
- [ ] Verify the authentication & access right of the order modification request from app
- [ ] Prevent replay attack

