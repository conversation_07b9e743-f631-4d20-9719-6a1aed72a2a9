/**
 * GraphQL Queries Integration Tests
 * 测试所有GraphQL查询操作
 */

const { createTestClient } = require('apollo-server-testing');
const { ApolloServer } = require('apollo-server-express');
const typeDefs = require('../../../graphql/schema/index');
const resolvers = require('../../../graphql/resolvers/index');
const { connectTestDB, clearTestDB, closeTestDB } = require('../../helpers/dbHelper');
const UserFactory = require('../../factories/userFactory');
const RestaurantFactory = require('../../factories/restaurantFactory');
const { generateAuthToken } = require('../../helpers/authHelper');

describe('GraphQL Queries Integration Tests', () => {
  let server;
  let query;
  let mutate;
  let testUser;
  let authToken;

  beforeAll(async () => {
    await connectTestDB();
    
    // Create Apollo Server for testing
    server = new ApolloServer({
      typeDefs,
      resolvers,
      context: ({ req }) => {
        // Mock authentication context
        return {
          req: {
            isAuth: true,
            userId: testUser?._id,
            userType: testUser?.userType || 'customer'
          }
        };
      }
    });

    const testClient = createTestClient(server);
    query = testClient.query;
    mutate = testClient.mutate;
  });

  beforeEach(async () => {
    await clearTestDB();
    
    // Create test user
    testUser = await UserFactory.createCustomer({
      email: '<EMAIL>',
      name: 'Test User'
    });
    
    authToken = generateAuthToken(testUser._id, testUser.userType);
  });

  afterAll(async () => {
    await closeTestDB();
  });

  describe('Restaurant Queries', () => {
    let testRestaurants;

    beforeEach(async () => {
      // Create test restaurants
      testRestaurants = await RestaurantFactory.createMany(3, {
        isActive: true,
        location: {
          type: 'Point',
          coordinates: [-73.935242, 40.730610] // NYC coordinates
        }
      });
    });

    describe('restaurants query', () => {
      const RESTAURANTS_QUERY = `
        query GetRestaurants($location: LocationInput, $limit: Int, $offset: Int) {
          restaurants(location: $location, limit: $limit, offset: $offset) {
            _id
            name
            description
            isActive
            location {
              coordinates
            }
            categories {
              _id
              title
              foods {
                _id
                title
                price
              }
            }
          }
        }
      `;

      test('should return all active restaurants', async () => {
        const response = await query({
          query: RESTAURANTS_QUERY,
          variables: {
            limit: 10,
            offset: 0
          }
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.restaurants).toHaveLength(3);
        expect(response.data.restaurants[0]).toHaveProperty('_id');
        expect(response.data.restaurants[0]).toHaveProperty('name');
        expect(response.data.restaurants[0].isActive).toBe(true);
      });

      test('should filter restaurants by location', async () => {
        const response = await query({
          query: RESTAURANTS_QUERY,
          variables: {
            location: {
              latitude: 40.730610,
              longitude: -73.935242,
              radius: 5000 // 5km radius
            },
            limit: 10,
            offset: 0
          }
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.restaurants).toHaveLength(3);
      });

      test('should support pagination', async () => {
        const response1 = await query({
          query: RESTAURANTS_QUERY,
          variables: {
            limit: 2,
            offset: 0
          }
        });

        const response2 = await query({
          query: RESTAURANTS_QUERY,
          variables: {
            limit: 2,
            offset: 2
          }
        });

        expect(response1.errors).toBeUndefined();
        expect(response2.errors).toBeUndefined();
        expect(response1.data.restaurants).toHaveLength(2);
        expect(response2.data.restaurants).toHaveLength(1);
        
        // Ensure no overlap
        const ids1 = response1.data.restaurants.map(r => r._id);
        const ids2 = response2.data.restaurants.map(r => r._id);
        expect(ids1).not.toEqual(expect.arrayContaining(ids2));
      });

      test('should return empty array when no restaurants match location', async () => {
        const response = await query({
          query: RESTAURANTS_QUERY,
          variables: {
            location: {
              latitude: 0,
              longitude: 0,
              radius: 1000
            }
          }
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.restaurants).toHaveLength(0);
      });
    });

    describe('restaurant query', () => {
      const RESTAURANT_QUERY = `
        query GetRestaurant($id: String!) {
          restaurant(id: $id) {
            _id
            name
            description
            isActive
            location {
              coordinates
            }
            categories {
              _id
              title
              foods {
                _id
                title
                description
                price
                variations {
                  _id
                  title
                  price
                  discounted
                }
              }
            }
            addons {
              _id
              title
              description
              quantityMinimum
              quantityMaximum
              options {
                _id
                title
                price
              }
            }
          }
        }
      `;

      test('should return restaurant by ID', async () => {
        const restaurant = testRestaurants[0];

        const response = await query({
          query: RESTAURANT_QUERY,
          variables: {
            id: restaurant._id.toString()
          }
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.restaurant).toBeDefined();
        expect(response.data.restaurant._id).toBe(restaurant._id.toString());
        expect(response.data.restaurant.name).toBe(restaurant.name);
      });

      test('should return null for non-existent restaurant', async () => {
        const response = await query({
          query: RESTAURANT_QUERY,
          variables: {
            id: '507f1f77bcf86cd799439011' // Valid ObjectId that doesn't exist
          }
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.restaurant).toBeNull();
      });

      test('should return error for invalid restaurant ID', async () => {
        const response = await query({
          query: RESTAURANT_QUERY,
          variables: {
            id: 'invalid-id'
          }
        });

        expect(response.errors).toBeDefined();
        expect(response.errors[0].message).toContain('Cast to ObjectId failed');
      });
    });
  });

  describe('User Queries', () => {
    describe('profile query', () => {
      const PROFILE_QUERY = `
        query GetProfile {
          profile {
            _id
            name
            email
            userType
            isActive
            addresses {
              _id
              label
              deliveryAddress
              details
              selected
            }
          }
        }
      `;

      test('should return current user profile', async () => {
        const response = await query({
          query: PROFILE_QUERY
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.profile).toBeDefined();
        expect(response.data.profile._id).toBe(testUser._id.toString());
        expect(response.data.profile.email).toBe(testUser.email);
        expect(response.data.profile.name).toBe(testUser.name);
      });

      test('should return error when not authenticated', async () => {
        // Create server without authentication
        const unauthServer = new ApolloServer({
          typeDefs,
          resolvers,
          context: () => ({
            req: { isAuth: false }
          })
        });

        const { query: unauthQuery } = createTestClient(unauthServer);

        const response = await unauthQuery({
          query: PROFILE_QUERY
        });

        expect(response.errors).toBeDefined();
        expect(response.errors[0].message).toContain('Unauthenticated');
      });
    });

    describe('users query', () => {
      const USERS_QUERY = `
        query GetUsers($role: String, $isActive: Boolean) {
          users(role: $role, isActive: $isActive) {
            _id
            name
            email
            userType
            isActive
          }
        }
      `;

      beforeEach(async () => {
        // Create additional test users
        await UserFactory.createCustomer({ name: 'Customer 1', isActive: true });
        await UserFactory.createCustomer({ name: 'Customer 2', isActive: false });
        await UserFactory.createRestaurant({ name: 'Restaurant Owner', isActive: true });
        await UserFactory.createRider({ name: 'Rider 1', isActive: true });
      });

      test('should return all users', async () => {
        const response = await query({
          query: USERS_QUERY
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.users).toHaveLength(5); // Including the test user
      });

      test('should filter users by role', async () => {
        const response = await query({
          query: USERS_QUERY,
          variables: {
            role: 'customer'
          }
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.users).toHaveLength(3); // 3 customers
        expect(response.data.users.every(user => user.userType === 'customer')).toBe(true);
      });

      test('should filter users by active status', async () => {
        const response = await query({
          query: USERS_QUERY,
          variables: {
            isActive: true
          }
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.users.every(user => user.isActive === true)).toBe(true);
      });

      test('should filter users by role and active status', async () => {
        const response = await query({
          query: USERS_QUERY,
          variables: {
            role: 'customer',
            isActive: true
          }
        });

        expect(response.errors).toBeUndefined();
        expect(response.data.users).toHaveLength(2); // 2 active customers
        expect(response.data.users.every(user => 
          user.userType === 'customer' && user.isActive === true
        )).toBe(true);
      });
    });
  });
});
