/**
 * Restaurant GraphQL API Integration Tests
 * 测试餐厅相关的GraphQL查询和变更
 */

const request = require('supertest');
const app = require('../../../app'); // Use the main app

describe('Restaurant GraphQL API Integration Tests', () => {

  describe('GraphQL Endpoint', () => {
    test('should respond to GraphQL endpoint', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __schema {
                types {
                  name
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.__schema).toBeDefined();
    });

    test('should handle invalid GraphQL queries', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              invalidField
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('Restaurant Schema', () => {
    test('should have Restaurant type in schema', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __type(name: "Restaurant") {
                name
                fields {
                  name
                  type {
                    name
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('Restaurant');
    });

  });

  describe('Basic GraphQL Operations', () => {
    test('should handle simple queries', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __typename
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__typename).toBe('Query');
    });

    test('should validate GraphQL syntax', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              invalidSyntax {
          `
        });

      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
    });

    test('should handle empty queries', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: ''
        });

      expect(response.status).toBe(400);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('Schema Introspection', () => {
    test('should support schema introspection', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query IntrospectionQuery {
              __schema {
                queryType {
                  name
                }
                mutationType {
                  name
                }
                types {
                  name
                  kind
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__schema).toBeDefined();
      expect(response.body.data.__schema.queryType.name).toBe('Query');
      expect(response.body.data.__schema.mutationType.name).toBe('Mutation');
      expect(Array.isArray(response.body.data.__schema.types)).toBe(true);
    });

    test('should list available queries', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __schema {
                queryType {
                  fields {
                    name
                    description
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__schema.queryType.fields).toBeDefined();
      expect(Array.isArray(response.body.data.__schema.queryType.fields)).toBe(true);
    });

    test('should list available mutations', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __schema {
                mutationType {
                  fields {
                    name
                    description
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__schema.mutationType.fields).toBeDefined();
      expect(Array.isArray(response.body.data.__schema.mutationType.fields)).toBe(true);
    });
  });
});
