/**
 * Order GraphQL API Integration Tests
 * 测试订单相关的GraphQL查询和变更
 */

const request = require('supertest');
const app = require('../../../app');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../../helpers/testDatabase');
const { createOrder } = require('../../factories/orderFactory');
const { createRestaurant } = require('../../factories/restaurantFactory');
const { createCustomer } = require('../../factories/customerFactory');
const { generateAuthToken } = require('../../helpers/authHelper');

describe('Order GraphQL API Integration Tests', () => {
  let testOrder;
  let testRestaurant;
  let testCustomer;
  let authToken;

  beforeAll(async () => {
    await connectTestDB();
  });

  afterAll(async () => {
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();

    // Create test data
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    testOrder = await createOrder({
      user: testCustomer._id,
      restaurant: testRestaurant._id
    });
    authToken = generateAuthToken(testCustomer._id);
  });

  describe('Order Schema Types', () => {
    test('should have Order type in schema', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __type(name: "Order") {
                name
                fields {
                  name
                  type {
                    name
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('Order');
    });

    test('should have OrderStatus enum in schema', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __type(name: "OrderStatus") {
                name
                kind
                enumValues {
                  name
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('OrderStatus');
      expect(response.body.data.__type.kind).toBe('ENUM');
    });

  });

  describe('Order Input Types', () => {
    test('should have OrderInput type in schema', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __type(name: "OrderInput") {
                name
                kind
                inputFields {
                  name
                  type {
                    name
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('OrderInput');
      expect(response.body.data.__type.kind).toBe('INPUT_OBJECT');
    });

    test('should validate GraphQL order operations', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __schema {
                mutationType {
                  fields {
                    name
                    args {
                      name
                      type {
                        name
                      }
                    }
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__schema.mutationType.fields).toBeDefined();

      // Check if order-related mutations exist
      const mutations = response.body.data.__schema.mutationType.fields;
      const orderMutations = mutations.filter(field =>
        field.name.toLowerCase().includes('order')
      );
      expect(orderMutations.length).toBeGreaterThan(0);
    });
  });

  describe('GraphQL Error Handling', () => {
    test('should handle malformed order queries', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              order {
                invalidField
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
    });

    test('should validate required arguments', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              order {
                _id
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
    });
  });
});
