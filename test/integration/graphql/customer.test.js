/**
 * Customer GraphQL API Integration Tests
 * 测试客户相关的GraphQL查询和变更
 */

const request = require('supertest');
const app = require('../../../app');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../../helpers/testDatabase');
const { createCustomer, createCustomerAddress } = require('../../factories/customerFactory');
const { generateAuthToken } = require('../../helpers/authHelper');

describe('Customer GraphQL API Integration Tests', () => {
  let testCustomer;
  let testAddress;
  let authToken;

  beforeAll(async () => {
    await connectTestDB();
  });

  afterAll(async () => {
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();

    // Create test data
    testCustomer = await createCustomer();
    testAddress = await createCustomerAddress({ customer: testCustomer._id });
    authToken = generateAuthToken(testCustomer._id);
  });

  describe('Customer Schema Types', () => {
    test('should have Customer type in schema', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __type(name: "Customer") {
                name
                fields {
                  name
                  type {
                    name
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('Customer');
    });

    test('should have Address type in schema', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __type(name: "Address") {
                name
                fields {
                  name
                  type {
                    name
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('Address');
    });

  });

  describe('Customer Input Types', () => {
    test('should have ProfileInput type in schema', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __type(name: "ProfileInput") {
                name
                kind
                inputFields {
                  name
                  type {
                    name
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('ProfileInput');
      expect(response.body.data.__type.kind).toBe('INPUT_OBJECT');
    });

    test('should have AddressInput type in schema', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __type(name: "AddressInput") {
                name
                kind
                inputFields {
                  name
                  type {
                    name
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__type).toBeDefined();
      expect(response.body.data.__type.name).toBe('AddressInput');
      expect(response.body.data.__type.kind).toBe('INPUT_OBJECT');
    });
  });

  describe('Customer Operations', () => {
    test('should validate customer-related mutations exist', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              __schema {
                mutationType {
                  fields {
                    name
                    args {
                      name
                      type {
                        name
                      }
                    }
                  }
                }
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.data.__schema.mutationType.fields).toBeDefined();

      // Check if customer-related mutations exist
      const mutations = response.body.data.__schema.mutationType.fields;
      const customerMutations = mutations.filter(field =>
        field.name.toLowerCase().includes('profile') ||
        field.name.toLowerCase().includes('address') ||
        field.name.toLowerCase().includes('customer')
      );
      expect(customerMutations.length).toBeGreaterThan(0);
    });

    test('should handle GraphQL validation errors', async () => {
      const response = await request(app)
        .post('/graphql')
        .send({
          query: `
            query {
              profile {
                invalidField
              }
            }
          `
        });

      expect(response.status).toBe(200);
      expect(response.body.errors).toBeDefined();
    });
  });
});
