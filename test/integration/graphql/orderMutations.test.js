/**
 * GraphQL order mutations integration tests
 */

const { connectTestDB, clearCollections, findById } = require('../../helpers/dbHelper');
const { generateAuthToken } = require('../../helpers/authHelper');
const { graphqlMutation, cleanupApp } = require('../../helpers/apiHelper');
const { mockStripeCheckoutCreate, cleanAllMocks } = require('../../helpers/mockHelper');
const UserFactory = require('../../factories/userFactory');
const RestaurantFactory = require('../../factories/restaurantFactory');

describe('Order GraphQL Mutations', () => {
  let user, restaurant, authToken;
  
  // Connect to test database before all tests
  beforeAll(async () => {
    await connectTestDB();
  });
  
  // Set up test data before each test
  beforeEach(async () => {
    // Clear collections
    await clearCollections('User', 'Restaurant', 'Order');
    
    // Create test user
    user = await UserFactory.createCustomer();
    
    // Create test restaurant
    restaurant = await RestaurantFactory.create({
      owner: user._id
    });
    
    // Generate auth token
    authToken = generateAuthToken(user);
    
    // Reset mocks
    jest.clearAllMocks();
    cleanAllMocks();
  });
  
  // Clean up after all tests
  afterAll(async () => {
    // Clean up server
    await cleanupApp();

    // Clean up mocks
    jest.restoreAllMocks();
    cleanAllMocks();
  });
  
  test('should create a new order', async () => {
    // Mock Stripe checkout creation
    mockStripeCheckoutCreate({
      id: 'cs_test_123',
      url: 'https://checkout.stripe.com/test'
    });
    
    // Define order input
    const orderInput = {
      restaurantId: restaurant._id.toString(),
      orderInput: [
        {
          food: 'food-id',
          variation: 'variation-id',
          quantity: 2,
          addons: []
        }
      ],
      isPickedUp: true,
      instructions: 'Test instructions'
    };
    
    // Define GraphQL mutation
    const mutation = `
      mutation PlaceOrder($input: OrderInput!) {
        placeOrder(orderInput: $input) {
          _id
          status
          orderAmount
          restaurant {
            _id
            name
          }
          customer {
            _id
            name
          }
          isPickedUp
          instructions
        }
      }
    `;
    
    // Execute mutation
    const response = await graphqlMutation(
      mutation,
      { input: orderInput },
      authToken
    );
    
    // Verify response
    expect(response.status).toBe(200);
    expect(response.body.data).toBeTruthy();
    expect(response.body.data.placeOrder).toBeTruthy();
    
    // Verify order data
    const order = response.body.data.placeOrder;
    expect(order._id).toBeTruthy();
    expect(order.status).toBe('PENDING');
    expect(order.restaurant._id).toBe(restaurant._id.toString());
    expect(order.customer._id).toBe(user._id.toString());
    expect(order.isPickedUp).toBe(true);
    expect(order.instructions).toBe('Test instructions');
    
    // Note: Since we're using mock API, we can't verify database records
    // In a real integration test, this would verify the order was saved to the database
  });
  
  test('should fail to create order with invalid restaurant ID', async () => {
    // Define order input with invalid restaurant ID
    const orderInput = {
      restaurantId: 'invalid-restaurant-id',
      orderInput: [
        {
          food: 'food-id',
          variation: 'variation-id',
          quantity: 2,
          addons: []
        }
      ],
      isPickedUp: true
    };
    
    // Define GraphQL mutation
    const mutation = `
      mutation PlaceOrder($input: OrderInput!) {
        placeOrder(orderInput: $input) {
          _id
          status
        }
      }
    `;
    
    // Execute mutation
    const response = await graphqlMutation(
      mutation,
      { input: orderInput },
      authToken
    );
    
    // Verify response contains error
    expect(response.status).toBe(200);
    expect(response.body.errors).toBeTruthy();
    expect(response.body.errors[0].message).toContain('Restaurant not found');
  });
  
  test('should fail to create order without authentication', async () => {
    // Define order input
    const orderInput = {
      restaurantId: restaurant._id.toString(),
      orderInput: [
        {
          food: 'food-id',
          variation: 'variation-id',
          quantity: 2,
          addons: []
        }
      ],
      isPickedUp: true
    };
    
    // Define GraphQL mutation
    const mutation = `
      mutation PlaceOrder($input: OrderInput!) {
        placeOrder(orderInput: $input) {
          _id
          status
        }
      }
    `;
    
    // Execute mutation without auth token
    const response = await graphqlMutation(
      mutation,
      { input: orderInput }
    );
    
    // Verify response contains authentication error
    expect(response.status).toBe(200);
    expect(response.body.errors).toBeTruthy();
    expect(response.body.errors[0].message).toContain('Authentication required');
  });
  
  test('should update order status', async () => {
    // First create an order
    const orderInput = {
      restaurantId: restaurant._id.toString(),
      orderInput: [
        {
          food: 'food-id',
          variation: 'variation-id',
          quantity: 2,
          addons: []
        }
      ],
      isPickedUp: true
    };
    
    const createMutation = `
      mutation PlaceOrder($input: OrderInput!) {
        placeOrder(orderInput: $input) {
          _id
          status
        }
      }
    `;
    
    const createResponse = await graphqlMutation(
      createMutation,
      { input: orderInput },
      authToken
    );
    
    const orderId = createResponse.body.data.placeOrder._id;
    
    // Now update the order status
    const updateMutation = `
      mutation UpdateOrderStatus($id: ID!, $status: OrderStatus!) {
        updateOrderStatus(id: $id, status: $status) {
          _id
          status
          updatedAt
        }
      }
    `;
    
    const updateResponse = await graphqlMutation(
      updateMutation,
      { id: orderId, status: 'ACCEPTED' },
      authToken
    );
    
    // Verify response
    expect(updateResponse.status).toBe(200);
    expect(updateResponse.body.data).toBeTruthy();
    expect(updateResponse.body.data.updateOrderStatus).toBeTruthy();
    expect(updateResponse.body.data.updateOrderStatus.status).toBe('ACCEPTED');
    
    // Note: Since we're using mock API, we can't verify database records
    // In a real integration test, this would verify the order status was updated in the database
  });
});
