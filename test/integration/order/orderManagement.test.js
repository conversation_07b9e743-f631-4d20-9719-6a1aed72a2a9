/**
 * Order Management Integration Tests
 * 测试订单管理系统的集成功能 - 使用现有的GraphQL API
 */

const request = require('supertest');
const app = require('../../../app');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../../helpers/testDatabase');
const { createOrder } = require('../../factories/orderFactory');
const { createCustomer } = require('../../factories/customerFactory');
const { createRestaurant } = require('../../factories/restaurantFactory');
const { generateAuthToken } = require('../../helpers/authHelper');

describe('Order Management Integration Tests', () => {
  let testOrder;
  let testCustomer;
  let testRestaurant;
  let authToken;

  beforeAll(async () => {
    await connectTestDB();
  });

  afterAll(async () => {
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
    
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    testOrder = await createOrder({
      user: testCustomer._id,
      restaurant: testRestaurant._id
    });
    authToken = generateAuthToken(testCustomer._id);
  });

  describe('Order Retrieval via GraphQL', () => {
    test('should get order by ID', async () => {
      const query = `
        query GetOrder($id: String!) {
          order(id: $id) {
            _id
            orderId
            orderStatus
            orderAmount
            customerId
            items {
              _id
              title
              quantity
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ 
          query, 
          variables: { id: testOrder._id.toString() }
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.order).toBeDefined();
      expect(response.body.data.order._id).toBe(testOrder._id.toString());
      expect(response.body.data.order.orderStatus).toBeDefined();
    });

    test('should get orders by restaurant', async () => {
      const query = `
        query GetOrdersByRestaurant($restaurantId: ID!) {
          orders(restaurantId: $restaurantId) {
            _id
            orderId
            orderStatus
            orderAmount
            customerId
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ 
          query, 
          variables: { restaurantId: testRestaurant._id.toString() }
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.orders).toBeDefined();
      expect(Array.isArray(response.body.data.orders)).toBe(true);
    });

    test('should get orders by customer', async () => {
      const query = `
        query GetOrdersByCustomer($customerId: String!) {
          ordersByCustomer(customerId: $customerId) {
            _id
            orderId
            orderStatus
            orderAmount
            restaurant {
              _id
              name
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ 
          query, 
          variables: { customerId: testCustomer.customerId }
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.ordersByCustomer).toBeDefined();
      expect(Array.isArray(response.body.data.ordersByCustomer)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle non-existent order', async () => {
      const query = `
        query GetOrder($id: String!) {
          order(id: $id) {
            _id
            orderId
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ 
          query, 
          variables: { id: '507f1f77bcf86cd799439011' }
        })
        .expect(200);

      expect(response.body.data.order).toBeNull();
    });

    test('should handle unauthorized access', async () => {
      const query = `
        query GetOrder($id: String!) {
          order(id: $id) {
            _id
            orderId
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .send({ 
          query, 
          variables: { id: testOrder._id.toString() }
        })
        .expect(200);

      expect(response.body.errors).toBeDefined();
      expect(response.body.errors[0].message).toContain('Unauthenticated');
    });
  });

  describe('Order Analytics via GraphQL', () => {
    test('should get basic order statistics', async () => {
      // 创建多个测试订单
      await createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        orderStatus: 'COMPLETED',
        orderAmount: 25.99
      });

      await createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        orderStatus: 'PENDING',
        orderAmount: 15.50
      });

      const query = `
        query GetRestaurantOrders($restaurantId: ID!) {
          orders(restaurantId: $restaurantId) {
            _id
            orderStatus
            orderAmount
            orderDate
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ 
          query, 
          variables: { restaurantId: testRestaurant._id.toString() }
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.data.orders).toBeDefined();
      expect(response.body.data.orders.length).toBeGreaterThan(0);

      // 验证订单数据
      const orders = response.body.data.orders;
      orders.forEach(order => {
        expect(order._id).toBeDefined();
        expect(order.orderStatus).toBeDefined();
        expect(order.orderAmount).toBeGreaterThan(0);
      });
    });

    test('should validate order data integrity', async () => {
      const query = `
        query GetOrderDetails($id: String!) {
          order(id: $id) {
            _id
            orderId
            orderStatus
            orderAmount
            orderDate
            customerId
            restaurant {
              _id
              name
            }
            items {
              _id
              title
              quantity
              price
            }
          }
        }
      `;

      const response = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ 
          query, 
          variables: { id: testOrder._id.toString() }
        })
        .expect(200);

      expect(response.body.data).toBeDefined();
      const order = response.body.data.order;
      
      // 验证必需字段
      expect(order._id).toBeDefined();
      expect(order.orderId).toBeDefined();
      expect(order.orderStatus).toBeDefined();
      expect(order.orderAmount).toBeGreaterThan(0);
      expect(order.orderDate).toBeDefined();
      expect(order.customerId).toBeDefined();
      
      // 验证关联数据
      expect(order.restaurant).toBeDefined();
      expect(order.restaurant._id).toBe(testRestaurant._id.toString());
      
      // 验证订单项目
      expect(Array.isArray(order.items)).toBe(true);
      if (order.items.length > 0) {
        order.items.forEach(item => {
          expect(item._id).toBeDefined();
          expect(item.quantity).toBeGreaterThan(0);
        });
      }
    });
  });
});
