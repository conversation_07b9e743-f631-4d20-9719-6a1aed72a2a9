/**
 * Stripe Payment Integration Tests
 * 测试Stripe支付系统的集成功能
 */

const request = require('supertest');
const app = require('../../../app');
const { createOrder } = require('../../factories/orderFactory');
const { createCustomer } = require('../../factories/customerFactory');
const { createRestaurant } = require('../../factories/restaurantFactory');

// Mock Stripe
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => ({
    paymentIntents: {
      create: jest.fn(),
      retrieve: jest.fn(),
      confirm: jest.fn(),
      cancel: jest.fn()
    },
    webhooks: {
      constructEvent: jest.fn()
    },
    customers: {
      create: jest.fn(),
      retrieve: jest.fn()
    }
  }));
});

describe('Stripe Payment Integration Tests', () => {
  let testOrder;
  let testCustomer;
  let testRestaurant;
  let stripeInstance;

  beforeAll(async () => {
    // Get mocked Stripe instance
    const Stripe = require('stripe');
    stripeInstance = new Stripe();
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Create test data
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    testOrder = await createOrder({
      user: testCustomer._id,
      restaurant: testRestaurant._id,
      orderAmount: 25.99,
      paymentMethod: 'STRIPE'
    });
  });

  describe('Payment Intent Creation', () => {
    test('should create payment intent for order', async () => {
      const mockPaymentIntent = {
        id: 'pi_test_123',
        amount: 2599,
        currency: 'usd',
        status: 'requires_payment_method',
        client_secret: 'pi_test_123_secret'
      };

      stripeInstance.paymentIntents.create.mockResolvedValue(mockPaymentIntent);

      const response = await request(app)
        .post('/stripe/create-payment-intent')
        .send({
          orderId: testOrder._id.toString(),
          amount: 2599,
          currency: 'usd'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.paymentIntent).toBeDefined();
      expect(response.body.paymentIntent.id).toBe('pi_test_123');
      expect(stripeInstance.paymentIntents.create).toHaveBeenCalledWith({
        amount: 2599,
        currency: 'usd',
        metadata: {
          orderId: testOrder._id.toString()
        }
      });
    });

    test('should handle payment intent creation errors', async () => {
      stripeInstance.paymentIntents.create.mockRejectedValue(
        new Error('Invalid amount')
      );

      const response = await request(app)
        .post('/stripe/create-payment-intent')
        .send({
          orderId: testOrder._id.toString(),
          amount: -100,
          currency: 'usd'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    test('should validate required fields', async () => {
      const response = await request(app)
        .post('/stripe/create-payment-intent')
        .send({
          // Missing required fields
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('required');
    });
  });

  describe('Payment Confirmation', () => {
    test('should confirm payment intent', async () => {
      const mockConfirmedPayment = {
        id: 'pi_test_123',
        status: 'succeeded',
        amount: 2599,
        currency: 'usd'
      };

      stripeInstance.paymentIntents.confirm.mockResolvedValue(mockConfirmedPayment);

      const response = await request(app)
        .post('/stripe/confirm-payment')
        .send({
          paymentIntentId: 'pi_test_123',
          paymentMethodId: 'pm_test_456'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.paymentIntent.status).toBe('succeeded');
      expect(stripeInstance.paymentIntents.confirm).toHaveBeenCalledWith(
        'pi_test_123',
        {
          payment_method: 'pm_test_456'
        }
      );
    });

    test('should handle payment confirmation failures', async () => {
      stripeInstance.paymentIntents.confirm.mockRejectedValue(
        new Error('Your card was declined')
      );

      const response = await request(app)
        .post('/stripe/confirm-payment')
        .send({
          paymentIntentId: 'pi_test_123',
          paymentMethodId: 'pm_test_456'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('declined');
    });
  });

  describe('Payment Status Retrieval', () => {
    test('should retrieve payment intent status', async () => {
      const mockPaymentIntent = {
        id: 'pi_test_123',
        status: 'succeeded',
        amount: 2599,
        currency: 'usd',
        metadata: {
          orderId: testOrder._id.toString()
        }
      };

      stripeInstance.paymentIntents.retrieve.mockResolvedValue(mockPaymentIntent);

      const response = await request(app)
        .get('/stripe/payment-status/pi_test_123')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.status).toBe('succeeded');
      expect(response.body.orderId).toBe(testOrder._id.toString());
    });

    test('should handle non-existent payment intent', async () => {
      stripeInstance.paymentIntents.retrieve.mockRejectedValue(
        new Error('No such payment_intent')
      );

      const response = await request(app)
        .get('/stripe/payment-status/pi_nonexistent')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });
  });

  describe('Webhook Handling', () => {
    test('should handle payment succeeded webhook', async () => {
      const mockEvent = {
        id: 'evt_test_123',
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test_123',
            status: 'succeeded',
            amount: 2599,
            metadata: {
              orderId: testOrder._id.toString()
            }
          }
        }
      };

      stripeInstance.webhooks.constructEvent.mockReturnValue(mockEvent);

      const response = await request(app)
        .post('/stripe/webhook')
        .set('stripe-signature', 'test_signature')
        .send(JSON.stringify(mockEvent))
        .expect(200);

      expect(response.body.received).toBe(true);
      expect(stripeInstance.webhooks.constructEvent).toHaveBeenCalled();
    });

    test('should handle payment failed webhook', async () => {
      const mockEvent = {
        id: 'evt_test_124',
        type: 'payment_intent.payment_failed',
        data: {
          object: {
            id: 'pi_test_123',
            status: 'requires_payment_method',
            last_payment_error: {
              message: 'Your card was declined.'
            },
            metadata: {
              orderId: testOrder._id.toString()
            }
          }
        }
      };

      stripeInstance.webhooks.constructEvent.mockReturnValue(mockEvent);

      const response = await request(app)
        .post('/stripe/webhook')
        .set('stripe-signature', 'test_signature')
        .send(JSON.stringify(mockEvent))
        .expect(200);

      expect(response.body.received).toBe(true);
    });

    test('should validate webhook signature', async () => {
      stripeInstance.webhooks.constructEvent.mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      const response = await request(app)
        .post('/stripe/webhook')
        .set('stripe-signature', 'invalid_signature')
        .send('{"test": "data"}')
        .expect(400);

      expect(response.body.error).toContain('signature');
    });
  });

  describe('Customer Management', () => {
    test('should create Stripe customer', async () => {
      const mockCustomer = {
        id: 'cus_test_123',
        email: testCustomer.email,
        name: testCustomer.name
      };

      stripeInstance.customers.create.mockResolvedValue(mockCustomer);

      const response = await request(app)
        .post('/stripe/create-customer')
        .send({
          email: testCustomer.email,
          name: testCustomer.name,
          customerId: testCustomer._id.toString()
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.customer.id).toBe('cus_test_123');
      expect(stripeInstance.customers.create).toHaveBeenCalledWith({
        email: testCustomer.email,
        name: testCustomer.name,
        metadata: {
          customerId: testCustomer._id.toString()
        }
      });
    });

    test('should retrieve Stripe customer', async () => {
      const mockCustomer = {
        id: 'cus_test_123',
        email: testCustomer.email,
        name: testCustomer.name
      };

      stripeInstance.customers.retrieve.mockResolvedValue(mockCustomer);

      const response = await request(app)
        .get('/stripe/customer/cus_test_123')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.customer.id).toBe('cus_test_123');
    });
  });

  describe('Error Handling', () => {
    test('should handle Stripe API errors gracefully', async () => {
      const stripeError = new Error('Rate limit exceeded');
      stripeError.type = 'StripeRateLimitError';
      stripeError.statusCode = 429;

      stripeInstance.paymentIntents.create.mockRejectedValue(stripeError);

      const response = await request(app)
        .post('/stripe/create-payment-intent')
        .send({
          orderId: testOrder._id.toString(),
          amount: 2599,
          currency: 'usd'
        })
        .expect(429);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Rate limit');
    });

    test('should handle network errors', async () => {
      stripeInstance.paymentIntents.create.mockRejectedValue(
        new Error('Network error')
      );

      const response = await request(app)
        .post('/stripe/create-payment-intent')
        .send({
          orderId: testOrder._id.toString(),
          amount: 2599,
          currency: 'usd'
        })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('Payment Flow Integration', () => {
    test('should complete full payment flow', async () => {
      // Step 1: Create payment intent
      const mockPaymentIntent = {
        id: 'pi_test_123',
        amount: 2599,
        currency: 'usd',
        status: 'requires_payment_method',
        client_secret: 'pi_test_123_secret'
      };

      stripeInstance.paymentIntents.create.mockResolvedValue(mockPaymentIntent);

      const createResponse = await request(app)
        .post('/stripe/create-payment-intent')
        .send({
          orderId: testOrder._id.toString(),
          amount: 2599,
          currency: 'usd'
        })
        .expect(200);

      expect(createResponse.body.success).toBe(true);

      // Step 2: Confirm payment
      const mockConfirmedPayment = {
        ...mockPaymentIntent,
        status: 'succeeded'
      };

      stripeInstance.paymentIntents.confirm.mockResolvedValue(mockConfirmedPayment);

      const confirmResponse = await request(app)
        .post('/stripe/confirm-payment')
        .send({
          paymentIntentId: 'pi_test_123',
          paymentMethodId: 'pm_test_456'
        })
        .expect(200);

      expect(confirmResponse.body.success).toBe(true);
      expect(confirmResponse.body.paymentIntent.status).toBe('succeeded');
    });
  });
});
