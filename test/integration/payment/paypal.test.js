/**
 * PayPal Payment Integration Tests
 * 测试PayPal支付系统的集成功能
 */

const request = require('supertest');
const app = require('../../../app');
const { createOrder } = require('../../factories/orderFactory');
const { createCustomer } = require('../../factories/customerFactory');
const { createRestaurant } = require('../../factories/restaurantFactory');

// Mock PayPal SDK
jest.mock('@paypal/checkout-server-sdk', () => ({
  core: {
    PayPalHttpClient: jest.fn().mockImplementation(() => ({
      execute: jest.fn()
    })),
    SandboxEnvironment: jest.fn(),
    LiveEnvironment: jest.fn()
  },
  orders: {
    OrdersCreateRequest: jest.fn().mockImplementation(() => ({
      requestBody: jest.fn(),
      headers: {}
    })),
    OrdersCaptureRequest: jest.fn().mockImplementation(() => ({
      headers: {}
    })),
    OrdersGetRequest: jest.fn().mockImplementation(() => ({
      headers: {}
    }))
  }
}));

describe('PayPal Payment Integration Tests', () => {
  let testOrder;
  let testCustomer;
  let testRestaurant;
  let mockPayPalClient;

  beforeAll(async () => {
    // Get mocked PayPal client
    const paypal = require('@paypal/checkout-server-sdk');
    mockPayPalClient = new paypal.core.PayPalHttpClient();
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Create test data
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    testOrder = await createOrder({
      user: testCustomer._id,
      restaurant: testRestaurant._id,
      orderAmount: 25.99,
      paymentMethod: 'PAYPAL'
    });
  });

  describe('Order Creation', () => {
    test('should create PayPal order', async () => {
      const mockPayPalOrder = {
        result: {
          id: 'ORDER_123',
          status: 'CREATED',
          links: [
            {
              rel: 'approve',
              href: 'https://www.sandbox.paypal.com/checkoutnow?token=ORDER_123'
            }
          ]
        }
      };

      mockPayPalClient.execute.mockResolvedValue(mockPayPalOrder);

      const response = await request(app)
        .post('/paypal/create-order')
        .send({
          orderId: testOrder._id.toString(),
          amount: '25.99',
          currency: 'USD'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.orderID).toBe('ORDER_123');
      expect(response.body.approvalUrl).toContain('checkoutnow');
      expect(mockPayPalClient.execute).toHaveBeenCalled();
    });

    test('should handle order creation errors', async () => {
      mockPayPalClient.execute.mockRejectedValue(
        new Error('Invalid amount')
      );

      const response = await request(app)
        .post('/paypal/create-order')
        .send({
          orderId: testOrder._id.toString(),
          amount: '-25.99',
          currency: 'USD'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    test('should validate required fields', async () => {
      const response = await request(app)
        .post('/paypal/create-order')
        .send({
          // Missing required fields
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('required');
    });
  });

  describe('Order Capture', () => {
    test('should capture PayPal order', async () => {
      const mockCaptureResult = {
        result: {
          id: 'ORDER_123',
          status: 'COMPLETED',
          purchase_units: [
            {
              payments: {
                captures: [
                  {
                    id: 'CAPTURE_123',
                    status: 'COMPLETED',
                    amount: {
                      currency_code: 'USD',
                      value: '25.99'
                    }
                  }
                ]
              }
            }
          ]
        }
      };

      mockPayPalClient.execute.mockResolvedValue(mockCaptureResult);

      const response = await request(app)
        .post('/paypal/capture-order')
        .send({
          orderID: 'ORDER_123',
          orderId: testOrder._id.toString()
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.status).toBe('COMPLETED');
      expect(response.body.captureID).toBe('CAPTURE_123');
      expect(mockPayPalClient.execute).toHaveBeenCalled();
    });

    test('should handle capture failures', async () => {
      const mockCaptureError = {
        result: {
          id: 'ORDER_123',
          status: 'FAILED',
          details: [
            {
              issue: 'INSTRUMENT_DECLINED',
              description: 'The instrument presented was either declined by the processor or bank, or it can\'t be used for this payment.'
            }
          ]
        }
      };

      mockPayPalClient.execute.mockResolvedValue(mockCaptureError);

      const response = await request(app)
        .post('/paypal/capture-order')
        .send({
          orderID: 'ORDER_123',
          orderId: testOrder._id.toString()
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('DECLINED');
    });

    test('should handle non-existent orders', async () => {
      mockPayPalClient.execute.mockRejectedValue(
        new Error('Order not found')
      );

      const response = await request(app)
        .post('/paypal/capture-order')
        .send({
          orderID: 'NONEXISTENT_ORDER',
          orderId: testOrder._id.toString()
        })
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });
  });

  describe('Order Status Retrieval', () => {
    test('should retrieve PayPal order status', async () => {
      const mockOrderDetails = {
        result: {
          id: 'ORDER_123',
          status: 'APPROVED',
          purchase_units: [
            {
              amount: {
                currency_code: 'USD',
                value: '25.99'
              },
              custom_id: testOrder._id.toString()
            }
          ]
        }
      };

      mockPayPalClient.execute.mockResolvedValue(mockOrderDetails);

      const response = await request(app)
        .get('/paypal/order-status/ORDER_123')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.status).toBe('APPROVED');
      expect(response.body.amount).toBe('25.99');
      expect(response.body.orderId).toBe(testOrder._id.toString());
    });

    test('should handle non-existent order status requests', async () => {
      mockPayPalClient.execute.mockRejectedValue(
        new Error('Order not found')
      );

      const response = await request(app)
        .get('/paypal/order-status/NONEXISTENT_ORDER')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not found');
    });
  });

  describe('Webhook Handling', () => {
    test('should handle payment completed webhook', async () => {
      const mockWebhookEvent = {
        id: 'WH-123',
        event_type: 'PAYMENT.CAPTURE.COMPLETED',
        resource: {
          id: 'CAPTURE_123',
          status: 'COMPLETED',
          amount: {
            currency_code: 'USD',
            value: '25.99'
          },
          custom_id: testOrder._id.toString()
        }
      };

      const response = await request(app)
        .post('/paypal/webhook')
        .set('paypal-transmission-id', 'test-transmission-id')
        .set('paypal-cert-id', 'test-cert-id')
        .set('paypal-transmission-sig', 'test-signature')
        .set('paypal-transmission-time', new Date().toISOString())
        .send(mockWebhookEvent)
        .expect(200);

      expect(response.body.received).toBe(true);
    });

    test('should handle payment failed webhook', async () => {
      const mockWebhookEvent = {
        id: 'WH-124',
        event_type: 'PAYMENT.CAPTURE.DENIED',
        resource: {
          id: 'CAPTURE_124',
          status: 'DENIED',
          reason_code: 'DECLINED',
          custom_id: testOrder._id.toString()
        }
      };

      const response = await request(app)
        .post('/paypal/webhook')
        .set('paypal-transmission-id', 'test-transmission-id')
        .set('paypal-cert-id', 'test-cert-id')
        .set('paypal-transmission-sig', 'test-signature')
        .set('paypal-transmission-time', new Date().toISOString())
        .send(mockWebhookEvent)
        .expect(200);

      expect(response.body.received).toBe(true);
    });

    test('should validate webhook signature', async () => {
      const response = await request(app)
        .post('/paypal/webhook')
        .send({ test: 'data' })
        .expect(400);

      expect(response.body.error).toContain('signature');
    });
  });

  describe('Refund Processing', () => {
    test('should process PayPal refund', async () => {
      const mockRefundResult = {
        result: {
          id: 'REFUND_123',
          status: 'COMPLETED',
          amount: {
            currency_code: 'USD',
            value: '25.99'
          }
        }
      };

      mockPayPalClient.execute.mockResolvedValue(mockRefundResult);

      const response = await request(app)
        .post('/paypal/refund')
        .send({
          captureId: 'CAPTURE_123',
          amount: '25.99',
          currency: 'USD',
          orderId: testOrder._id.toString()
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.refundId).toBe('REFUND_123');
      expect(response.body.status).toBe('COMPLETED');
    });

    test('should handle partial refunds', async () => {
      const mockRefundResult = {
        result: {
          id: 'REFUND_124',
          status: 'COMPLETED',
          amount: {
            currency_code: 'USD',
            value: '10.00'
          }
        }
      };

      mockPayPalClient.execute.mockResolvedValue(mockRefundResult);

      const response = await request(app)
        .post('/paypal/refund')
        .send({
          captureId: 'CAPTURE_123',
          amount: '10.00',
          currency: 'USD',
          orderId: testOrder._id.toString()
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.refundId).toBe('REFUND_124');
      expect(response.body.amount).toBe('10.00');
    });

    test('should handle refund failures', async () => {
      mockPayPalClient.execute.mockRejectedValue(
        new Error('Refund not allowed')
      );

      const response = await request(app)
        .post('/paypal/refund')
        .send({
          captureId: 'CAPTURE_123',
          amount: '25.99',
          currency: 'USD',
          orderId: testOrder._id.toString()
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('not allowed');
    });
  });

  describe('Error Handling', () => {
    test('should handle PayPal API errors gracefully', async () => {
      const paypalError = new Error('Rate limit exceeded');
      paypalError.statusCode = 429;

      mockPayPalClient.execute.mockRejectedValue(paypalError);

      const response = await request(app)
        .post('/paypal/create-order')
        .send({
          orderId: testOrder._id.toString(),
          amount: '25.99',
          currency: 'USD'
        })
        .expect(429);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Rate limit');
    });

    test('should handle network errors', async () => {
      mockPayPalClient.execute.mockRejectedValue(
        new Error('Network error')
      );

      const response = await request(app)
        .post('/paypal/create-order')
        .send({
          orderId: testOrder._id.toString(),
          amount: '25.99',
          currency: 'USD'
        })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('Payment Flow Integration', () => {
    test('should complete full PayPal payment flow', async () => {
      // Step 1: Create order
      const mockCreateResult = {
        result: {
          id: 'ORDER_123',
          status: 'CREATED',
          links: [
            {
              rel: 'approve',
              href: 'https://www.sandbox.paypal.com/checkoutnow?token=ORDER_123'
            }
          ]
        }
      };

      mockPayPalClient.execute.mockResolvedValueOnce(mockCreateResult);

      const createResponse = await request(app)
        .post('/paypal/create-order')
        .send({
          orderId: testOrder._id.toString(),
          amount: '25.99',
          currency: 'USD'
        })
        .expect(200);

      expect(createResponse.body.success).toBe(true);
      expect(createResponse.body.orderID).toBe('ORDER_123');

      // Step 2: Capture order
      const mockCaptureResult = {
        result: {
          id: 'ORDER_123',
          status: 'COMPLETED',
          purchase_units: [
            {
              payments: {
                captures: [
                  {
                    id: 'CAPTURE_123',
                    status: 'COMPLETED',
                    amount: {
                      currency_code: 'USD',
                      value: '25.99'
                    }
                  }
                ]
              }
            }
          ]
        }
      };

      mockPayPalClient.execute.mockResolvedValueOnce(mockCaptureResult);

      const captureResponse = await request(app)
        .post('/paypal/capture-order')
        .send({
          orderID: 'ORDER_123',
          orderId: testOrder._id.toString()
        })
        .expect(200);

      expect(captureResponse.body.success).toBe(true);
      expect(captureResponse.body.status).toBe('COMPLETED');
      expect(captureResponse.body.captureID).toBe('CAPTURE_123');
    });
  });
});
