/**
 * Payment System Integration Tests
 * 测试通用支付系统的集成功能
 */

const request = require('supertest');
const app = require('../../../app');
const { createOrder } = require('../../factories/orderFactory');
const { createCustomer } = require('../../factories/customerFactory');
const { createRestaurant } = require('../../factories/restaurantFactory');

describe('Payment System Integration Tests', () => {
  let testOrder;
  let testCustomer;
  let testRestaurant;

  beforeEach(async () => {
    // Create test data
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    testOrder = await createOrder({
      user: testCustomer._id,
      restaurant: testRestaurant._id,
      orderAmount: 25.99
    });
  });

  describe('Payment Method Selection', () => {
    test('should list available payment methods', async () => {
      const response = await request(app)
        .get('/payment/methods')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.methods)).toBe(true);
      expect(response.body.methods.length).toBeGreaterThan(0);
      
      // Should include common payment methods
      const methodNames = response.body.methods.map(m => m.name);
      expect(methodNames).toContain('STRIPE');
      expect(methodNames).toContain('PAYPAL');
    });

    test('should validate payment method availability', async () => {
      const response = await request(app)
        .post('/payment/validate-method')
        .send({
          method: 'STRIPE',
          amount: 25.99,
          currency: 'USD'
        })
        .expect(200);

      expect(response.body.valid).toBe(true);
      expect(response.body.method).toBe('STRIPE');
    });

    test('should reject invalid payment methods', async () => {
      const response = await request(app)
        .post('/payment/validate-method')
        .send({
          method: 'INVALID_METHOD',
          amount: 25.99,
          currency: 'USD'
        })
        .expect(400);

      expect(response.body.valid).toBe(false);
      expect(response.body.error).toContain('Invalid payment method');
    });
  });

  describe('Payment Processing', () => {
    test('should process payment with Stripe', async () => {
      const response = await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'STRIPE',
          amount: 25.99,
          currency: 'USD',
          paymentData: {
            paymentMethodId: 'pm_test_123'
          }
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.method).toBe('STRIPE');
      expect(response.body.transactionId).toBeDefined();
    });

    test('should process payment with PayPal', async () => {
      const response = await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'PAYPAL',
          amount: 25.99,
          currency: 'USD',
          paymentData: {
            orderID: 'PAYPAL_ORDER_123'
          }
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.method).toBe('PAYPAL');
      expect(response.body.transactionId).toBeDefined();
    });

    test('should handle payment processing errors', async () => {
      const response = await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'STRIPE',
          amount: -25.99, // Invalid amount
          currency: 'USD',
          paymentData: {
            paymentMethodId: 'pm_test_123'
          }
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  describe('Payment Status Tracking', () => {
    test('should track payment status', async () => {
      const response = await request(app)
        .get(`/payment/status/${testOrder._id}`)
        .expect(200);

      expect(response.body.orderId).toBe(testOrder._id.toString());
      expect(response.body.status).toBeDefined();
      expect(['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED']).toContain(response.body.status);
    });

    test('should handle non-existent order status requests', async () => {
      const response = await request(app)
        .get('/payment/status/507f1f77bcf86cd799439011')
        .expect(404);

      expect(response.body.error).toContain('Order not found');
    });

    test('should update payment status', async () => {
      const response = await request(app)
        .put(`/payment/status/${testOrder._id}`)
        .send({
          status: 'COMPLETED',
          transactionId: 'txn_123',
          method: 'STRIPE'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.status).toBe('COMPLETED');
    });
  });

  describe('Refund Processing', () => {
    test('should process full refund', async () => {
      const response = await request(app)
        .post('/payment/refund')
        .send({
          orderId: testOrder._id.toString(),
          amount: 25.99,
          reason: 'Customer request',
          method: 'STRIPE',
          transactionId: 'txn_123'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.refundId).toBeDefined();
      expect(response.body.amount).toBe(25.99);
    });

    test('should process partial refund', async () => {
      const response = await request(app)
        .post('/payment/refund')
        .send({
          orderId: testOrder._id.toString(),
          amount: 10.00,
          reason: 'Partial cancellation',
          method: 'STRIPE',
          transactionId: 'txn_123'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.refundId).toBeDefined();
      expect(response.body.amount).toBe(10.00);
    });

    test('should validate refund amount', async () => {
      const response = await request(app)
        .post('/payment/refund')
        .send({
          orderId: testOrder._id.toString(),
          amount: 50.00, // More than original amount
          reason: 'Invalid refund',
          method: 'STRIPE',
          transactionId: 'txn_123'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('exceeds');
    });
  });

  describe('Payment History', () => {
    test('should retrieve payment history for order', async () => {
      const response = await request(app)
        .get(`/payment/history/${testOrder._id}`)
        .expect(200);

      expect(response.body.orderId).toBe(testOrder._id.toString());
      expect(Array.isArray(response.body.transactions)).toBe(true);
    });

    test('should retrieve payment history for customer', async () => {
      const response = await request(app)
        .get(`/payment/customer-history/${testCustomer._id}`)
        .expect(200);

      expect(response.body.customerId).toBe(testCustomer._id.toString());
      expect(Array.isArray(response.body.transactions)).toBe(true);
    });

    test('should filter payment history by date range', async () => {
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      const endDate = new Date();

      const response = await request(app)
        .get(`/payment/history/${testOrder._id}`)
        .query({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        })
        .expect(200);

      expect(response.body.orderId).toBe(testOrder._id.toString());
      expect(Array.isArray(response.body.transactions)).toBe(true);
    });
  });

  describe('Payment Analytics', () => {
    test('should provide payment analytics', async () => {
      const response = await request(app)
        .get('/payment/analytics')
        .query({
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          endDate: new Date().toISOString()
        })
        .expect(200);

      expect(response.body.totalTransactions).toBeDefined();
      expect(response.body.totalAmount).toBeDefined();
      expect(response.body.successRate).toBeDefined();
      expect(response.body.methodBreakdown).toBeDefined();
    });

    test('should provide payment method statistics', async () => {
      const response = await request(app)
        .get('/payment/analytics/methods')
        .expect(200);

      expect(Array.isArray(response.body.methods)).toBe(true);
      response.body.methods.forEach(method => {
        expect(method.name).toBeDefined();
        expect(method.count).toBeDefined();
        expect(method.totalAmount).toBeDefined();
      });
    });

    test('should provide failure analysis', async () => {
      const response = await request(app)
        .get('/payment/analytics/failures')
        .expect(200);

      expect(response.body.totalFailures).toBeDefined();
      expect(response.body.failureRate).toBeDefined();
      expect(Array.isArray(response.body.reasons)).toBe(true);
    });
  });

  describe('Security and Validation', () => {
    test('should validate payment amounts', async () => {
      const response = await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'STRIPE',
          amount: 0, // Invalid amount
          currency: 'USD',
          paymentData: {
            paymentMethodId: 'pm_test_123'
          }
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('amount');
    });

    test('should validate currency codes', async () => {
      const response = await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'STRIPE',
          amount: 25.99,
          currency: 'INVALID', // Invalid currency
          paymentData: {
            paymentMethodId: 'pm_test_123'
          }
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('currency');
    });

    test('should prevent duplicate payments', async () => {
      // First payment
      await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'STRIPE',
          amount: 25.99,
          currency: 'USD',
          paymentData: {
            paymentMethodId: 'pm_test_123'
          }
        })
        .expect(200);

      // Duplicate payment attempt
      const response = await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'STRIPE',
          amount: 25.99,
          currency: 'USD',
          paymentData: {
            paymentMethodId: 'pm_test_123'
          }
        })
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('already processed');
    });
  });

  describe('Error Handling', () => {
    test('should handle network timeouts gracefully', async () => {
      const response = await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'TIMEOUT_TEST', // Special test method that simulates timeout
          amount: 25.99,
          currency: 'USD',
          paymentData: {}
        })
        .expect(408);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('timeout');
    });

    test('should handle service unavailable errors', async () => {
      const response = await request(app)
        .post('/payment/process')
        .send({
          orderId: testOrder._id.toString(),
          method: 'UNAVAILABLE_TEST', // Special test method that simulates service unavailable
          amount: 25.99,
          currency: 'USD',
          paymentData: {}
        })
        .expect(503);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('unavailable');
    });
  });
});
