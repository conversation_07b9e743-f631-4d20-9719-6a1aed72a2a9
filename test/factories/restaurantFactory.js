/**
 * Restaurant factory for generating test data
 */

const { faker } = require('@faker-js/faker');
const mongoose = require('mongoose');

// Import models
const Restaurant = require('../../models/restaurant');
const Owner = require('../../models/owner');

class RestaurantFactory {
  /**
   * Build a restaurant object without saving to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Object} Restaurant object
   */
  static build(overrides = {}) {
    return {
      name: faker.company.name(),
      address: faker.location.streetAddress(), // Restaurant model expects a string, not an object
      image: faker.image.url(),
      logo: faker.image.url(),
      orderPrefix: faker.string.alpha(3).toUpperCase(),
      orderId: 1,
      deliveryTime: faker.number.int({ min: 15, max: 60 }),
      minimumOrder: faker.number.float({ min: 5, max: 25, multipleOf: 0.01 }),
      isActive: true,
      location: {
        type: 'Point',
        coordinates: [
          parseFloat(faker.location.longitude()),
          parseFloat(faker.location.latitude())
        ]
      },
      username: faker.internet.userName(),
      password: faker.internet.password(),
      sections: [],
      enableNotification: true,
      isAvailable: true,
      commissionRate: 25,
      cuisines: [faker.helpers.arrayElement(['Italian', 'Chinese', 'Mexican', 'Indian', 'Japanese'])],
      tax: 10,
      reviewCount: 0,
      reviewAverage: 0,
      keywords: [],
      tags: [],
      phone: faker.phone.number(),
      deliveryCostType: 'fixed',
      deliveryCostMin: 3.0,
      _testData: true, // Mark as test data for easy cleanup
      ...overrides
    };
  }
  
  /**
   * Create a restaurant and save to database
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Object>} Created restaurant
   */
  static async create(overrides = {}) {
    // Ensure mongoose is connected before creating
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState === 0) {
      // Try to connect if not already connected
      const { connectTestDB } = require('../helpers/dbHelper');
      await connectTestDB();
    }

    const restaurantData = this.build(overrides);
    return await Restaurant.create(restaurantData);
  }
  
  /**
   * Create multiple restaurants
   * @param {number} count - Number of restaurants to create
   * @param {Object} overrides - Properties to override defaults
   * @returns {Promise<Array>} Created restaurants
   */
  static async createMany(count, overrides = {}) {
    const restaurants = [];
    
    for (let i = 0; i < count; i++) {
      restaurants.push(this.build(overrides));
    }
    
    return await Restaurant.insertMany(restaurants);
  }
  
  /**
   * Create a restaurant with menu items
   * @param {Object} overrides - Properties to override defaults
   * @param {number} itemCount - Number of menu items to create
   * @returns {Promise<Object>} Created restaurant with menu items
   */
  static async createWithMenu(overrides = {}, itemCount = 5) {
    // This is a placeholder - actual implementation would depend on
    // how menu items are stored in the database
    const restaurant = await this.create(overrides);
    
    // Create menu items for the restaurant
    // This would need to be implemented based on the actual model structure
    
    return restaurant;
  }
}

/**
 * Create a restaurant (alias for RestaurantFactory.create)
 * @param {Object} overrides - Properties to override defaults
 * @returns {Promise<Object>} Created restaurant
 */
const createRestaurant = async (overrides = {}) => {
  return await RestaurantFactory.create(overrides);
};

/**
 * Create an owner
 * @param {Object} overrides - Properties to override defaults
 * @returns {Promise<Object>} Created owner
 */
const createOwner = async (overrides = {}) => {
  const defaultOwner = {
    email: faker.internet.email(),
    password: faker.internet.password(),
    userType: 'OWNER',
    restaurants: [],
    pushToken: faker.string.alphanumeric(64),
    _testData: true,
    ...overrides
  };

  // Ensure mongoose is connected before creating
  const mongoose = require('mongoose');
  if (mongoose.connection.readyState === 0) {
    const { connectTestDB } = require('../helpers/dbHelper');
    await connectTestDB();
  }

  return await Owner.create(defaultOwner);
};

/**
 * Create an owner with restaurants
 * @param {Object} ownerOverrides - Owner properties to override
 * @param {number} restaurantCount - Number of restaurants to create
 * @param {Object} restaurantOverrides - Restaurant properties to override
 * @returns {Promise<Object>} Created owner with restaurants
 */
const createOwnerWithRestaurants = async (ownerOverrides = {}, restaurantCount = 2, restaurantOverrides = {}) => {
  const owner = await createOwner(ownerOverrides);
  const restaurants = [];

  for (let i = 0; i < restaurantCount; i++) {
    const restaurant = await createRestaurant({
      owner: owner._id,
      ...restaurantOverrides
    });
    restaurants.push(restaurant);
  }

  // Update owner with restaurant references
  owner.restaurants = restaurants.map(r => r._id);
  await owner.save();

  return {
    owner,
    restaurants
  };
};

module.exports = {
  RestaurantFactory,
  createRestaurant,
  createOwner,
  createOwnerWithRestaurants
};
