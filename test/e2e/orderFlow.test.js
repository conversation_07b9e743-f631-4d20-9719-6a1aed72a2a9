/**
 * End-to-End Order Flow Tests
 * 测试完整的订单流程，从创建到交付
 * 
 * 这些测试使用真实的数据库和最少的mock
 */

const request = require('supertest');
const app = require('../../app');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const { createCustomer } = require('../factories/customerFactory');
const { createRestaurant, createOwner } = require('../factories/restaurantFactory');
const { generateAuthToken } = require('../helpers/authHelper');

describe('End-to-End Order Flow Tests', () => {
  let testCustomer;
  let testRestaurant;
  let testOwner;
  let customerAuthToken;
  let ownerAuthToken;

  beforeAll(async () => {
    await connectTestDB();
  });

  afterAll(async () => {
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
    
    // Create test entities
    testCustomer = await createCustomer({
      email: '<EMAIL>',
      phone: '+1234567890',
      name: 'Test Customer'
    });

    testOwner = await createOwner({
      email: '<EMAIL>',
      name: 'Test Owner'
    });

    testRestaurant = await createRestaurant({
      owner: testOwner._id,
      name: 'Test Restaurant',
      isActive: true,
      isAvailable: true,
      categories: [
        {
          _id: 'cat_1',
          title: 'Main Dishes',
          foods: [
            {
              _id: 'food_1',
              title: 'Burger',
              description: 'Delicious burger',
              price: 12.99,
              variations: [
                {
                  _id: 'var_1',
                  title: 'Regular',
                  price: 12.99,
                  discounted: 0
                }
              ]
            }
          ]
        }
      ]
    });

    customerAuthToken = generateAuthToken(testCustomer._id);
    ownerAuthToken = generateAuthToken(testOwner._id);
  });

  describe('Complete Order Lifecycle', () => {
    test('should complete full order flow from creation to delivery', async () => {
      // Step 1: Customer browses restaurant menu via GraphQL
      const menuQuery = `
        query GetRestaurant($id: ID!) {
          restaurant(id: $id) {
            _id
            name
            categories {
              _id
              title
              foods {
                _id
                title
                price
                variations {
                  _id
                  title
                  price
                }
              }
            }
          }
        }
      `;

      const menuResponse = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send({
          query: menuQuery,
          variables: { id: testRestaurant._id.toString() }
        })
        .expect(200);

      expect(menuResponse.body.data).toBeDefined();
      expect(menuResponse.body.data.restaurant).toBeDefined();
      expect(menuResponse.body.data.restaurant.categories).toBeDefined();
      expect(menuResponse.body.data.restaurant.categories.length).toBeGreaterThan(0);

      // Step 2: Customer creates order via GraphQL
      const orderMutation = `
        mutation PlaceOrder($restaurantId: ID!, $customerId: String!, $orderInput: [OrderInput!]!, $paymentMethod: String!, $deliveryAddressId: ID, $orderDate: String!, $isPickedUp: Boolean!, $taxationAmount: Float!, $deliveryCharges: Float!) {
          placeOrder(
            restaurantId: $restaurantId
            customerId: $customerId
            orderInput: $orderInput
            paymentMethod: $paymentMethod
            deliveryAddressId: $deliveryAddressId
            orderDate: $orderDate
            isPickedUp: $isPickedUp
            taxationAmount: $taxationAmount
            deliveryCharges: $deliveryCharges
          ) {
            _id
            orderId
            orderStatus
            orderAmount
            customerId
          }
        }
      `;

      const orderVariables = {
        restaurantId: testRestaurant._id.toString(),
        customerId: testCustomer.customerId,
        orderInput: [
          {
            food: 'food_1',
            variation: 'var_1',
            quantity: 2,
            specialInstructions: 'No onions'
          }
        ],
        paymentMethod: 'COD',
        deliveryAddressId: testCustomer.addresses[0].addressId,
        orderDate: new Date().toISOString(),
        isPickedUp: false,
        taxationAmount: 2.08,
        deliveryCharges: 3.00
      };

      const createOrderResponse = await request(app)
        .post('/graphql')
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send({
          query: orderMutation,
          variables: orderVariables
        })
        .expect(200);

      expect(createOrderResponse.body.data).toBeDefined();
      expect(createOrderResponse.body.data.placeOrder).toBeDefined();
      expect(createOrderResponse.body.data.placeOrder.orderStatus).toBeDefined();
      expect(createOrderResponse.body.data.placeOrder.orderAmount).toBeGreaterThan(0);

      const orderId = createOrderResponse.body.data.placeOrder._id;

      // Step 3: Restaurant owner views pending orders
      const pendingOrdersResponse = await request(app)
        .get(`/api/orders/restaurant/${testRestaurant._id}`)
        .query({ status: 'PENDING' })
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .expect(200);

      expect(pendingOrdersResponse.body.success).toBe(true);
      expect(pendingOrdersResponse.body.orders.length).toBe(1);
      expect(pendingOrdersResponse.body.orders[0]._id).toBe(orderId);

      // Step 4: Restaurant accepts order
      const acceptOrderResponse = await request(app)
        .put(`/api/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({
          status: 'ACCEPTED',
          estimatedTime: 30
        })
        .expect(200);

      expect(acceptOrderResponse.body.success).toBe(true);
      expect(acceptOrderResponse.body.order.orderStatus).toBe('ACCEPTED');
      expect(acceptOrderResponse.body.order.estimatedTime).toBe(30);
      expect(acceptOrderResponse.body.order.acceptedAt).toBeDefined();

      // Step 5: Customer checks order status
      const statusResponse = await request(app)
        .get(`/api/orders/${orderId}`)
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .expect(200);

      expect(statusResponse.body.success).toBe(true);
      expect(statusResponse.body.order.orderStatus).toBe('ACCEPTED');

      // Step 6: Restaurant starts preparing
      const preparingResponse = await request(app)
        .put(`/api/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({ status: 'PREPARING' })
        .expect(200);

      expect(preparingResponse.body.success).toBe(true);
      expect(preparingResponse.body.order.orderStatus).toBe('PREPARING');
      expect(preparingResponse.body.order.preparingAt).toBeDefined();

      // Step 7: Order is ready
      const readyResponse = await request(app)
        .put(`/api/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({ status: 'READY' })
        .expect(200);

      expect(readyResponse.body.success).toBe(true);
      expect(readyResponse.body.order.orderStatus).toBe('READY');
      expect(readyResponse.body.order.readyAt).toBeDefined();

      // Step 8: Order goes out for delivery
      const deliveryResponse = await request(app)
        .put(`/api/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({
          status: 'OUT_FOR_DELIVERY',
          riderId: 'rider_123'
        })
        .expect(200);

      expect(deliveryResponse.body.success).toBe(true);
      expect(deliveryResponse.body.order.orderStatus).toBe('OUT_FOR_DELIVERY');
      expect(deliveryResponse.body.order.riderId).toBe('rider_123');
      expect(deliveryResponse.body.order.outForDeliveryAt).toBeDefined();

      // Step 9: Order is delivered
      const deliveredResponse = await request(app)
        .put(`/api/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({ status: 'DELIVERED' })
        .expect(200);

      expect(deliveredResponse.body.success).toBe(true);
      expect(deliveredResponse.body.order.orderStatus).toBe('DELIVERED');
      expect(deliveredResponse.body.order.deliveredAt).toBeDefined();

      // Step 10: Verify order history
      const historyResponse = await request(app)
        .get(`/api/orders/${orderId}/history`)
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .expect(200);

      expect(historyResponse.body.success).toBe(true);
      expect(historyResponse.body.history.length).toBeGreaterThan(0);
      
      const statuses = historyResponse.body.history.map(h => h.status);
      expect(statuses).toContain('PENDING');
      expect(statuses).toContain('ACCEPTED');
      expect(statuses).toContain('PREPARING');
      expect(statuses).toContain('READY');
      expect(statuses).toContain('OUT_FOR_DELIVERY');
      expect(statuses).toContain('DELIVERED');
    });

    test('should handle order cancellation flow', async () => {
      // Create order
      const orderData = {
        restaurantId: testRestaurant._id.toString(),
        items: [
          {
            foodId: 'food_1',
            variationId: 'var_1',
            quantity: 1,
            price: 12.99
          }
        ],
        deliveryAddress: '123 Test Street',
        paymentMethod: 'CARD',
        deliveryCharges: 3.00,
        taxAmount: 1.28
      };

      const createResponse = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send(orderData)
        .expect(201);

      const orderId = createResponse.body.order._id;

      // Accept order first
      await request(app)
        .put(`/api/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({
          status: 'ACCEPTED',
          estimatedTime: 25
        })
        .expect(200);

      // Cancel order
      const cancelResponse = await request(app)
        .put(`/api/orders/${orderId}/cancel`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({
          reason: 'Restaurant is temporarily closed'
        })
        .expect(200);

      expect(cancelResponse.body.success).toBe(true);
      expect(cancelResponse.body.order.orderStatus).toBe('CANCELLED');
      expect(cancelResponse.body.order.cancelReason).toBe('Restaurant is temporarily closed');
      expect(cancelResponse.body.order.cancelledAt).toBeDefined();

      // Verify customer can see cancellation
      const statusResponse = await request(app)
        .get(`/api/orders/${orderId}`)
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .expect(200);

      expect(statusResponse.body.order.orderStatus).toBe('CANCELLED');
      expect(statusResponse.body.order.cancelReason).toBe('Restaurant is temporarily closed');
    });

    test('should handle order modification before acceptance', async () => {
      // Create order
      const orderData = {
        restaurantId: testRestaurant._id.toString(),
        items: [
          {
            foodId: 'food_1',
            variationId: 'var_1',
            quantity: 1,
            price: 12.99
          }
        ],
        deliveryAddress: '123 Test Street',
        paymentMethod: 'CARD',
        deliveryCharges: 3.00,
        taxAmount: 1.28
      };

      const createResponse = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send(orderData)
        .expect(201);

      const orderId = createResponse.body.order._id;

      // Modify order (add more items)
      const modifyResponse = await request(app)
        .put(`/api/orders/${orderId}/items`)
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send({
          items: [
            {
              foodId: 'food_1',
              variationId: 'var_1',
              quantity: 2, // Increase quantity
              price: 12.99
            }
          ]
        })
        .expect(200);

      expect(modifyResponse.body.success).toBe(true);
      expect(modifyResponse.body.order.orderAmount).toBeGreaterThan(createResponse.body.order.orderAmount);

      // Update delivery address
      const addressResponse = await request(app)
        .put(`/api/orders/${orderId}/address`)
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send({
          deliveryAddress: '456 New Street, New City'
        })
        .expect(200);

      expect(addressResponse.body.success).toBe(true);
      expect(addressResponse.body.order.deliveryAddress).toBe('456 New Street, New City');

      // Try to modify after acceptance (should fail)
      await request(app)
        .put(`/api/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({
          status: 'ACCEPTED',
          estimatedTime: 30
        })
        .expect(200);

      const failedModifyResponse = await request(app)
        .put(`/api/orders/${orderId}/items`)
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send({
          items: [
            {
              foodId: 'food_1',
              variationId: 'var_1',
              quantity: 3,
              price: 12.99
            }
          ]
        })
        .expect(400);

      expect(failedModifyResponse.body.success).toBe(false);
      expect(failedModifyResponse.body.error).toContain('cannot be modified');
    });
  });

  describe('Error Scenarios', () => {
    test('should handle invalid restaurant ID', async () => {
      const orderData = {
        restaurantId: '507f1f77bcf86cd799439011', // Non-existent ID
        items: [
          {
            foodId: 'food_1',
            variationId: 'var_1',
            quantity: 1,
            price: 12.99
          }
        ],
        deliveryAddress: '123 Test Street',
        paymentMethod: 'CARD'
      };

      const response = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send(orderData)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Restaurant not found');
    });

    test('should handle unauthorized access', async () => {
      // Try to access orders without authentication
      const response = await request(app)
        .get(`/api/orders/restaurant/${testRestaurant._id}`)
        .expect(401);

      expect(response.body.error).toContain('Unauthorized');
    });

    test('should handle invalid status transitions', async () => {
      // Create and accept order
      const orderData = {
        restaurantId: testRestaurant._id.toString(),
        items: [{ foodId: 'food_1', variationId: 'var_1', quantity: 1, price: 12.99 }],
        deliveryAddress: '123 Test Street',
        paymentMethod: 'CARD'
      };

      const createResponse = await request(app)
        .post('/api/orders')
        .set('Authorization', `Bearer ${customerAuthToken}`)
        .send(orderData)
        .expect(201);

      const orderId = createResponse.body.order._id;

      // Try invalid transition (PENDING -> DELIVERED)
      const response = await request(app)
        .put(`/api/orders/${orderId}/status`)
        .set('Authorization', `Bearer ${ownerAuthToken}`)
        .send({ status: 'DELIVERED' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid status transition');
    });
  });
});
