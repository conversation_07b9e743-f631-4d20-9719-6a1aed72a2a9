/**
 * Concurrency Testing Suite
 * 测试系统在并发场景下的数据一致性和竞态条件
 */

const request = require('supertest');
const app = require('../../app');
const { connectTestDB, disconnectTestDB, clearTestDB } = require('../helpers/testDatabase');
const { createCustomer } = require('../factories/customerFactory');
const { createRestaurant } = require('../factories/restaurantFactory');
const { createOrder } = require('../factories/orderFactory');
const { generateAuthToken } = require('../helpers/authHelper');
const Order = require('../../models/order');
const Customer = require('../../models/customer');

describe('Concurrency Testing', () => {
  let testCustomer;
  let testRestaurant;
  let authToken;

  beforeAll(async () => {
    await connectTestDB({ useRealDatabase: true });
  });

  afterAll(async () => {
    await disconnectTestDB();
  });

  beforeEach(async () => {
    await clearTestDB();
    
    testCustomer = await createCustomer();
    testRestaurant = await createRestaurant();
    authToken = generateAuthToken(testCustomer._id);
  });

  describe('Order Creation Concurrency', () => {
    test('should handle concurrent order creation without data corruption', async () => {
      const concurrentOrders = 50;
      const orderPromises = [];

      // 创建50个并发订单
      for (let i = 0; i < concurrentOrders; i++) {
        const orderData = {
          restaurantId: testRestaurant._id.toString(),
          customerId: testCustomer._id.toString(),
          items: [
            {
              foodId: `food_${i}`,
              quantity: 1,
              price: 10.00 + i
            }
          ],
          deliveryAddress: `Address ${i}`,
          paymentMethod: 'CARD'
        };

        orderPromises.push(
          request(app)
            .post('/api/orders')
            .set('Authorization', `Bearer ${authToken}`)
            .send(orderData)
            .expect(201)
        );
      }

      const results = await Promise.all(orderPromises);
      
      // 验证所有订单都成功创建
      expect(results.length).toBe(concurrentOrders);
      results.forEach((result, index) => {
        expect(result.body.success).toBe(true);
        expect(result.body.order.orderId).toBeDefined();
        expect(result.body.order.orderAmount).toBe(10.00 + index);
      });

      // 验证数据库中的订单数量
      const dbOrders = await Order.find({ user: testCustomer._id });
      expect(dbOrders.length).toBe(concurrentOrders);

      // 验证订单ID的唯一性
      const orderIds = dbOrders.map(order => order.orderId);
      const uniqueOrderIds = [...new Set(orderIds)];
      expect(uniqueOrderIds.length).toBe(concurrentOrders);
    });

    test('should handle concurrent order status updates correctly', async () => {
      // 创建一个订单
      const order = await createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        orderStatus: 'PENDING'
      });

      // 并发尝试更新订单状态
      const statusUpdates = [
        { status: 'ACCEPTED', estimatedTime: 30 },
        { status: 'ACCEPTED', estimatedTime: 25 },
        { status: 'ACCEPTED', estimatedTime: 35 }
      ];

      const updatePromises = statusUpdates.map(update =>
        request(app)
          .put(`/api/orders/${order._id}/status`)
          .set('Authorization', `Bearer ${authToken}`)
          .send(update)
      );

      const results = await Promise.allSettled(updatePromises);
      
      // 应该只有一个成功，其他的应该失败或被忽略
      const successfulUpdates = results.filter(result => 
        result.status === 'fulfilled' && result.value.status === 200
      );
      
      expect(successfulUpdates.length).toBeGreaterThanOrEqual(1);

      // 验证最终状态的一致性
      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.orderStatus).toBe('ACCEPTED');
      expect(finalOrder.acceptedAt).toBeDefined();
    });
  });

  describe('Payment Processing Concurrency', () => {
    test('should prevent duplicate payment processing', async () => {
      const order = await createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        orderAmount: 25.99
      });

      const paymentData = {
        orderId: order._id.toString(),
        method: 'STRIPE',
        amount: 25.99,
        currency: 'USD',
        paymentData: {
          paymentMethodId: 'pm_test_123'
        }
      };

      // 并发发送多个支付请求
      const paymentPromises = Array(5).fill().map(() =>
        request(app)
          .post('/api/payments/process')
          .set('Authorization', `Bearer ${authToken}`)
          .send(paymentData)
      );

      const results = await Promise.allSettled(paymentPromises);
      
      // 应该只有一个支付成功
      const successfulPayments = results.filter(result => 
        result.status === 'fulfilled' && result.value.status === 200
      );
      
      expect(successfulPayments.length).toBe(1);

      // 其他请求应该返回409 (冲突) 或类似错误
      const duplicatePayments = results.filter(result => 
        result.status === 'fulfilled' && result.value.status === 409
      );
      
      expect(duplicatePayments.length).toBeGreaterThan(0);
    });
  });

  describe('Database Transaction Concurrency', () => {
    test('should handle concurrent customer updates without race conditions', async () => {
      const customer = await createCustomer({
        name: 'Original Name',
        email: '<EMAIL>'
      });

      // 并发更新客户信息
      const updatePromises = Array(10).fill().map((_, index) =>
        Customer.findByIdAndUpdate(
          customer._id,
          { 
            name: `Updated Name ${index}`,
            updatedAt: new Date()
          },
          { new: true }
        )
      );

      const results = await Promise.all(updatePromises);
      
      // 所有更新都应该成功
      expect(results.length).toBe(10);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.name).toMatch(/Updated Name \d/);
      });

      // 验证最终状态
      const finalCustomer = await Customer.findById(customer._id);
      expect(finalCustomer.name).toMatch(/Updated Name \d/);
    });

    test('should handle concurrent order item updates correctly', async () => {
      const order = await createOrder({
        user: testCustomer._id,
        restaurant: testRestaurant._id,
        items: [
          { foodId: 'food_1', quantity: 1, price: 10.00 }
        ]
      });

      // 并发更新订单项目
      const updatePromises = Array(5).fill().map((_, index) =>
        request(app)
          .put(`/api/orders/${order._id}/items`)
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            items: [
              { foodId: 'food_1', quantity: index + 1, price: 10.00 }
            ]
          })
      );

      const results = await Promise.allSettled(updatePromises);
      
      // 验证至少有一个更新成功
      const successfulUpdates = results.filter(result => 
        result.status === 'fulfilled' && result.value.status === 200
      );
      
      expect(successfulUpdates.length).toBeGreaterThanOrEqual(1);

      // 验证最终状态的一致性
      const finalOrder = await Order.findById(order._id);
      expect(finalOrder.items[0].quantity).toBeGreaterThan(0);
      expect(finalOrder.items[0].quantity).toBeLessThanOrEqual(5);
    });
  });

  describe('Session and Cache Concurrency', () => {
    test('should handle concurrent session operations', async () => {
      const sessionService = require('../../whatsapp/services/sessionService');
      
      const sessionPromises = Array(20).fill().map((_, index) => {
        const sessionId = `test-session-${index}`;
        return sessionService.createSession(sessionId, {
          customerId: testCustomer._id.toString(),
          restaurantId: testRestaurant._id.toString(),
          step: 'MENU_SELECTION'
        });
      });

      const results = await Promise.all(sessionPromises);
      
      // 所有会话都应该成功创建
      expect(results.length).toBe(20);
      results.forEach((result, index) => {
        expect(result).toBeDefined();
        expect(result.customerId).toBe(testCustomer._id.toString());
      });

      // 验证会话的唯一性
      const sessionIds = results.map(result => result.sessionId);
      const uniqueSessionIds = [...new Set(sessionIds)];
      expect(uniqueSessionIds.length).toBe(20);
    });
  });

  describe('Resource Locking Tests', () => {
    test('should handle inventory updates with proper locking', async () => {
      // 模拟库存更新的并发操作
      const initialInventory = 100;
      let currentInventory = initialInventory;
      const orderQuantities = Array(50).fill(2); // 50个订单，每个订单2个商品

      const inventoryPromises = orderQuantities.map(async (quantity) => {
        // 模拟检查库存和减少库存的原子操作
        return new Promise((resolve) => {
          setTimeout(() => {
            if (currentInventory >= quantity) {
              currentInventory -= quantity;
              resolve({ success: true, remaining: currentInventory });
            } else {
              resolve({ success: false, error: 'Insufficient inventory' });
            }
          }, Math.random() * 10); // 随机延迟模拟真实场景
        });
      });

      const results = await Promise.all(inventoryPromises);
      
      const successfulOrders = results.filter(result => result.success);
      const failedOrders = results.filter(result => !result.success);

      // 验证库存计算的正确性
      const totalConsumed = successfulOrders.length * 2;
      expect(currentInventory).toBe(initialInventory - totalConsumed);
      expect(currentInventory).toBeGreaterThanOrEqual(0);

      console.log('Inventory Test Results:', {
        initialInventory,
        finalInventory: currentInventory,
        successfulOrders: successfulOrders.length,
        failedOrders: failedOrders.length,
        totalConsumed
      });
    });
  });

  describe('Error Handling Under Concurrency', () => {
    test('should gracefully handle errors in concurrent operations', async () => {
      // 创建一些会导致错误的并发请求
      const invalidRequests = Array(10).fill().map(() =>
        request(app)
          .post('/api/orders')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            // 缺少必需字段
            restaurantId: testRestaurant._id.toString()
          })
      );

      const validRequests = Array(10).fill().map((_, index) =>
        request(app)
          .post('/api/orders')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            restaurantId: testRestaurant._id.toString(),
            customerId: testCustomer._id.toString(),
            items: [{ foodId: `food_${index}`, quantity: 1, price: 10.00 }],
            deliveryAddress: `Address ${index}`,
            paymentMethod: 'CARD'
          })
      );

      const allRequests = [...invalidRequests, ...validRequests];
      const results = await Promise.allSettled(allRequests);

      // 验证错误请求被正确处理
      const errors = results.filter(result => 
        result.status === 'fulfilled' && result.value.status === 400
      );
      expect(errors.length).toBe(10);

      // 验证有效请求成功处理
      const successes = results.filter(result => 
        result.status === 'fulfilled' && result.value.status === 201
      );
      expect(successes.length).toBe(10);

      // 验证系统仍然稳定
      const healthCheck = await request(app)
        .get('/api/payments/methods')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(healthCheck.body.success).toBe(true);
    });
  });
});
