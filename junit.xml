<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="6" failures="6" errors="0" time="7.165">
  <testsuite name="End-to-End Order Flow Tests" errors="0" failures="6" skipped="0" timestamp="2025-06-08T22:32:20" time="7.126" tests="6">
    <testcase classname="End-to-End Order Flow Tests Complete Order Lifecycle should complete full order flow from creation to delivery" name="End-to-End Order Flow Tests Complete Order Lifecycle should complete full order flow from creation to delivery" time="0.001">
      <failure>MongooseError: Can&apos;t call `openUri()` on an active connection with different connection strings. Make sure you aren&apos;t calling `mongoose.connect()` multiple times. See: https://mongoosejs.com/docs/connections.html#multiple_connections
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:720:13)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:46:20)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:19:20)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/e2e/orderFlow.test.js:23:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="End-to-End Order Flow Tests Complete Order Lifecycle should handle order cancellation flow" name="End-to-End Order Flow Tests Complete Order Lifecycle should handle order cancellation flow" time="0">
      <failure>MongooseError: Can&apos;t call `openUri()` on an active connection with different connection strings. Make sure you aren&apos;t calling `mongoose.connect()` multiple times. See: https://mongoosejs.com/docs/connections.html#multiple_connections
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:720:13)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:46:20)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:19:20)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/e2e/orderFlow.test.js:23:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="End-to-End Order Flow Tests Complete Order Lifecycle should handle order modification before acceptance" name="End-to-End Order Flow Tests Complete Order Lifecycle should handle order modification before acceptance" time="0">
      <failure>MongooseError: Can&apos;t call `openUri()` on an active connection with different connection strings. Make sure you aren&apos;t calling `mongoose.connect()` multiple times. See: https://mongoosejs.com/docs/connections.html#multiple_connections
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:720:13)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:46:20)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:19:20)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/e2e/orderFlow.test.js:23:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="End-to-End Order Flow Tests Error Scenarios should handle invalid restaurant ID" name="End-to-End Order Flow Tests Error Scenarios should handle invalid restaurant ID" time="0">
      <failure>MongooseError: Can&apos;t call `openUri()` on an active connection with different connection strings. Make sure you aren&apos;t calling `mongoose.connect()` multiple times. See: https://mongoosejs.com/docs/connections.html#multiple_connections
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:720:13)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:46:20)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:19:20)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/e2e/orderFlow.test.js:23:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="End-to-End Order Flow Tests Error Scenarios should handle unauthorized access" name="End-to-End Order Flow Tests Error Scenarios should handle unauthorized access" time="0">
      <failure>MongooseError: Can&apos;t call `openUri()` on an active connection with different connection strings. Make sure you aren&apos;t calling `mongoose.connect()` multiple times. See: https://mongoosejs.com/docs/connections.html#multiple_connections
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:720:13)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:46:20)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:19:20)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/e2e/orderFlow.test.js:23:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="End-to-End Order Flow Tests Error Scenarios should handle invalid status transitions" name="End-to-End Order Flow Tests Error Scenarios should handle invalid status transitions" time="0">
      <failure>MongooseError: Can&apos;t call `openUri()` on an active connection with different connection strings. Make sure you aren&apos;t calling `mongoose.connect()` multiple times. See: https://mongoosejs.com/docs/connections.html#multiple_connections
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:720:13)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:46:20)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/testDatabase.js:19:20)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/e2e/orderFlow.test.js:23:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)</failure>
    </testcase>
  </testsuite>
</testsuites>